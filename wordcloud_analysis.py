#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
词云分析项目
功能：从网页链接提取文本，进行中文分词，生成词云图和词频统计
作者：AI Assistant
日期：2025年
"""

import requests
from bs4 import BeautifulSoup
import jieba
import jieba.analyse
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import pandas as pd
from PIL import Image
import numpy as np
import re
import os
from collections import Counter

class WordCloudAnalyzer:
    """词云分析器类"""
    
    def __init__(self):
        """初始化分析器"""
        self.text = ""
        self.words = []
        self.word_freq = {}
        self.filtered_words = []
        
        # 设置中文字体路径（Windows系统）
        self.font_path = 'C:/Windows/Fonts/simhei.ttf'  # 黑体
        
        # 定义停用词列表
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '我们', '你们', '他们', '她们', '它们', '这个', '那个', '这些', '那些', '这样', '那样', '这里', '那里', '现在', '以后', '以前', '今天', '明天', '昨天', '时候', '时间', '地方', '什么', '怎么', '为什么', '哪里', '哪个', '多少', '几个', '第一', '第二', '可以', '应该', '能够', '需要', '想要', '希望', '觉得', '认为', '知道', '明白', '理解', '发现', '出现', '开始', '结束', '继续', '停止', '进行', '实现', '完成', '成功', '失败', '问题', '方法', '方式', '情况', '状态', '结果', '原因', '目的', '意思', '内容', '信息', '数据', '材料', '东西', '事情', '工作', '学习', '生活', '社会', '国家', '世界', '年', '月', '日', '号', '点', '分', '秒', '元', '块', '毛', '角', '分', '个', '只', '件', '条', '张', '片', '本', '册', '部', '章', '节', '段', '句', '字', '词', '页', '行', '列', '项', '次', '遍', '回', '趟', '顿', '下', '上', '中', '里', '外', '前', '后', '左', '右', '东', '南', '西', '北', '及', '与', '或', '但', '而', '因', '所以', '如果', '虽然', '然而', '不过', '只是', '仅仅', '已经', '还是', '还有', '另外', '此外', '除了', '包括', '根据', '按照', '通过', '由于', '关于', '对于', '至于', '作为', '成为', '变成', '形成', '产生', '发生', '出现', '存在', '具有', '拥有', '得到', '获得', '取得', '达到', '到达', '来到', '回到', '走到', '跑到', '飞到', '游到', '爬到', '跳到', '坐到', '站到', '躺到', '睡到', '醒到', '起到', '倒到', '放到', '拿到', '给到', '送到', '带到', '领到', '接到', '迎到', '送到', '陪到', '跟到', '随到', '追到', '赶到', '等到', '盼到', '望到', '看到', '听到', '闻到', '尝到', '摸到', '感到', '觉到', '想到', '记到', '忘到', '学到', '教到', '告到', '说到', '讲到', '谈到', '聊到', '问到', '答到', '回到', '应到', '对到', '向到', '朝到', '往到', '从到', '自到', '为到', '被到', '让到', '使到', '令到', '叫到', '请到', '要到', '该到', '会到', '能到', '可到', '敢到', '肯到', '愿到', '想到', '打算', '计划', '准备', '决定', '选择', '确定', '肯定', '一定', '必须', '应当', '应该', '需要', '要求', '希望', '期望', '盼望', '渴望', '想象', '幻想', '梦想', '理想', '现实', '实际', '真实', '虚假', '正确', '错误', '对', '错', '是', '非', '好', '坏', '优', '劣', '高', '低', '大', '小', '多', '少', '新', '旧', '老', '嫩', '生', '熟', '冷', '热', '暖', '凉', '干', '湿', '软', '硬', '轻', '重', '快', '慢', '早', '晚', '远', '近', '深', '浅', '宽', '窄', '长', '短', '粗', '细', '厚', '薄', '胖', '瘦', '美', '丑', '亮', '暗', '明', '昏', '清', '浊', '净', '脏', '香', '臭', '甜', '苦', '酸', '辣', '咸', '淡', '响', '静', '吵', '闹', '安', '危', '全', '缺', '满', '空', '忙', '闲', '累', '轻松', '紧张', '放松', '兴奋', '平静', '高兴', '难过', '快乐', '痛苦', '幸福', '不幸', '满意', '不满', '同意', '反对', '赞成', '反对', '支持', '反对', '喜欢', '讨厌', '爱', '恨', '关心', '冷漠', '热情', '冷淡', '友好', '敌对', '和谐', '冲突', '合作', '竞争', '团结', '分裂', '统一', '分散', '集中', '分布', '聚集', '分离', '结合', '连接', '断开', '打开', '关闭', '开始', '结束', '继续', '停止', '前进', '后退', '上升', '下降', '增加', '减少', '提高', '降低', '改善', '恶化', '发展', '倒退', '进步', '落后', '成功', '失败', '胜利', '失败', '赢', '输', '得', '失', '获', '失去', '保持', '改变', '变化', '不变', '稳定', '波动', '增长', '下跌', '上涨', '下降'
        }
        
        # 需要过滤的特殊词汇（将在分析过程中识别并添加）
        self.custom_filter_words = set()
        
    def fetch_text_from_url(self, url):
        """从URL获取文本内容"""
        try:
            print(f"正在获取网页内容: {url}")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'

            print(f"HTTP状态码: {response.status_code}")

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 移除script和style标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 尝试多种方式提取文本内容
            text = ""

            # 方法1：查找文章内容区域
            article_selectors = [
                '.article-content',
                '.content',
                '.main-content',
                '.post-content',
                '.entry-content',
                'article',
                '.article',
                '[class*="content"]',
                '[class*="article"]'
            ]

            for selector in article_selectors:
                elements = soup.select(selector)
                if elements:
                    text = ' '.join([elem.get_text() for elem in elements])
                    break

            # 方法2：如果没找到特定区域，提取所有文本
            if not text or len(text) < 100:
                text = soup.get_text()

            # 清理文本
            text = re.sub(r'\s+', ' ', text)  # 替换多个空白字符为单个空格
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""''（）【】《》\-]', '', text)  # 保留中文、英文、数字、空格和基本标点

            # 进一步清理：移除过短的行和明显的导航/菜单文本
            lines = text.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if (len(line) > 10 and  # 行长度大于10
                    not re.match(r'^[0-9\s\-]+$', line) and  # 不是纯数字和符号
                    '登录' not in line and '注册' not in line and  # 不包含登录注册
                    '首页' not in line and '导航' not in line):  # 不包含导航元素
                    cleaned_lines.append(line)

            self.text = ' '.join(cleaned_lines).strip()
            print(f"成功获取文本，长度: {len(self.text)} 字符")

            # 如果文本太短，打印前500字符用于调试
            if len(self.text) < 500:
                print("文本内容预览:")
                print(self.text[:500])
                print("原始HTML预览:")
                print(response.text[:1000])

            return len(self.text) > 50  # 至少要有50个字符才算成功

        except Exception as e:
            print(f"获取网页内容失败: {e}")
            return False

    def segment_text(self):
        """对文本进行中文分词"""
        if not self.text:
            print("错误：没有文本内容可供分词")
            return False

        print("正在进行中文分词...")

        # 使用jieba进行分词
        words = jieba.cut(self.text)

        # 过滤词汇
        filtered_words = []
        for word in words:
            word = word.strip()
            # 过滤条件：
            # 1. 长度大于1
            # 2. 不在停用词列表中
            # 3. 不是纯数字
            # 4. 不是纯英文字母
            # 5. 包含中文字符
            if (len(word) > 1 and
                word not in self.stop_words and
                not word.isdigit() and
                not word.isalpha() and
                re.search(r'[\u4e00-\u9fa5]', word)):
                filtered_words.append(word)

        self.words = filtered_words
        print(f"分词完成，共获得 {len(self.words)} 个有效词汇")

        # 统计词频
        self.word_freq = Counter(self.words)
        print(f"词频统计完成，共有 {len(self.word_freq)} 个不同的词汇")

        return True

    def identify_abnormal_words(self):
        """识别并标记异常词汇"""
        print("正在识别异常词汇...")

        abnormal_words = set()

        # 检查词频统计中的异常词汇
        for word, freq in self.word_freq.items():
            # 标记异常词汇的条件：
            # 1. 包含特殊字符或符号
            # 2. 长度过长（超过10个字符）
            # 3. 包含数字和字母混合
            # 4. 重复字符过多
            # 5. 明显的网页元素（如"点击"、"链接"、"登录"等）

            if (re.search(r'[^\u4e00-\u9fa5a-zA-Z0-9]', word) or  # 包含特殊字符
                len(word) > 10 or  # 长度过长
                (re.search(r'\d', word) and re.search(r'[a-zA-Z]', word)) or  # 数字字母混合
                len(set(word)) < len(word) * 0.5 or  # 重复字符过多
                word in ['点击', '链接', '登录', '注册', '首页', '网站', '页面', '浏览器', '下载', '安装', '更新', '版本', '系统', '软件', '应用', '程序', '文件', '数据', '信息', '内容', '服务', '功能', '设置', '配置', '管理', '操作', '使用', '帮助', '支持', '联系', '关于', '隐私', '条款', '协议', '声明', '版权', '所有', '保留', '权利', '免责', '责任', '法律', '条件', '规定', '要求', '标准', '规范', '指南', '说明', '介绍', '概述', '详情', '具体', '特别', '注意', '提示', '警告', '错误', '失败', '成功', '完成', '处理', '加载', '刷新', '返回', '退出', '关闭', '打开', '启动', '停止', '暂停', '继续', '开始', '结束', '取消', '确认', '提交', '保存', '删除', '修改', '编辑', '查看', '搜索', '查找', '筛选', '排序', '分类', '标签', '分享', '收藏', '喜欢', '评论', '回复', '转发', '关注', '粉丝', '用户', '账号', '密码', '验证', '安全', '保护', '备份', '恢复', '同步', '导入', '导出', '上传', '下载', '发送', '接收', '传输', '连接', '网络', '在线', '离线', '实时', '即时', '快速', '高效', '便捷', '简单', '复杂', '高级', '基础', '专业', '个人', '企业', '商业', '免费', '付费', '试用', '体验', '测试', '演示', '样例', '模板', '格式', '类型', '种类', '方式', '方法', '技术', '工具', '平台', '环境', '框架', '库', '组件', '模块', '插件', '扩展', '主题', '皮肤', '界面', '布局', '设计', '风格', '颜色', '字体', '大小', '位置', '对齐', '间距', '边距', '边框', '背景', '前景', '透明', '阴影', '效果', '动画', '过渡', '变换', '缩放', '旋转', '移动', '滑动', '滚动', '拖拽', '点击', '双击', '右键', '长按', '滑动', '缩放', '旋转', '震动', '声音', '音效', '音乐', '视频', '图片', '图像', '照片', '截图', '录屏', '直播', '播放', '暂停', '停止', '快进', '快退', '重播', '循环', '随机', '顺序', '列表', '网格', '卡片', '标签', '按钮', '输入', '选择', '下拉', '弹窗', '对话', '提示', '通知', '消息', '邮件', '短信', '电话', '联系', '地址', '位置', '地图', '导航', '路线', '距离', '时间', '日期', '时区', '语言', '地区', '国家', '城市', '天气', '温度', '湿度', '风速', '气压', '能见度', '紫外线', '空气', '质量', '污染', '健康', '安全', '环保', '节能', '低碳', '绿色', '可持续', '发展', '创新', '科技', '智能', '人工', '机器', '自动', '手动', '远程', '本地', '云端', '服务器', '客户端', '浏览器', '移动', '桌面', '平板', '手机', '电脑', '笔记本', '台式', '一体', '显示器', '屏幕', '键盘', '鼠标', '触摸', '手势', '语音', '识别', '输入', '输出', '存储', '内存', '硬盘', '固态', '机械', '光驱', 'USB', '蓝牙', 'WiFi', '网线', '路由器', '交换机', '防火墙', '代理', 'VPN', 'DNS', 'IP', '域名', '端口', '协议', 'HTTP', 'HTTPS', 'FTP', 'SMTP', 'POP3', 'IMAP', 'TCP', 'UDP', 'SSL', 'TLS', 'API', 'SDK', 'IDE', '编程', '代码', '脚本', '算法', '数据结构', '数据库', 'SQL', 'NoSQL', '缓存', '队列', '栈', '树', '图', '哈希', '排序', '搜索', '遍历', '递归', '迭代', '循环', '条件', '判断', '分支', '异常', '错误', '调试', '测试', '部署', '发布', '版本', '控制', 'Git', 'SVN', '分支', '合并', '冲突', '解决', '提交', '推送', '拉取', '克隆', '仓库', '项目', '团队', '协作', '沟通', '交流', '讨论', '会议', '文档', '报告', '总结', '计划', '任务', '进度', '里程碑', '截止', '优先级', '重要', '紧急', '普通', '低', '高', '中', '级别', '等级', '分数', '评分', '评价', '反馈', '建议', '意见', '投诉', '表扬', '批评', '改进', '优化', '升级', '更新', '维护', '修复', '补丁', '漏洞', '安全', '风险', '威胁', '攻击', '防护', '加密', '解密', '签名', '验证', '授权', '认证', '权限', '角色', '用户组', '管理员', '普通用户', '游客', '访客', '会员', 'VIP', '高级', '普通', '基础', '免费', '付费', '订阅', '购买', '支付', '退款', '发票', '收据', '账单', '费用', '价格', '折扣', '优惠', '促销', '活动', '奖励', '积分', '等级', '经验', '成就', '徽章', '排行榜', '竞赛', '比赛', '游戏', '娱乐', '休闲', '学习', '教育', '培训', '课程', '教程', '指南', '手册', '文档', '帮助', '支持', '客服', '售后', '维修', '保修', '质保', '服务', '产品', '商品', '物品', '货物', '包裹', '快递', '物流', '配送', '运输', '仓储', '库存', '采购', '供应', '需求', '市场', '销售', '营销', '推广', '宣传', '广告', '品牌', '形象', '声誉', '口碑', '评价', '评论', '点评', '推荐', '分享', '传播', '影响', '效果', '结果', '成果', '收益', '利润', '成本', '投入', '产出', '效率', '质量', '数量', '规模', '范围', '领域', '行业', '专业', '技能', '能力', '水平', '标准', '要求', '条件', '资格', '证书', '认证', '考试', '测试', '评估', '审核', '检查', '监督', '管理', '控制', '调节', '调整', '改变', '变化', '发展', '进步', '提升', '改善', '优化', '完善', '创新', '突破', '发现', '发明', '研究', '开发', '设计', '制作', '生产', '制造', '加工', '处理', '操作', '使用', '应用', '实施', '执行', '实现', '达成', '完成', '成功', '失败', '错误', '问题', '困难', '挑战', '机会', '可能', '潜力', '前景', '未来', '趋势', '方向', '目标', '计划', '策略', '方案', '措施', '行动', '步骤', '流程', '程序', '规程', '制度', '规则', '法规', '政策', '法律', '条例', '规定', '标准', '规范', '准则', '原则', '理念', '观念', '思想', '理论', '概念', '定义', '含义', '意义', '价值', '作用', '功能', '特点', '特色', '优势', '劣势', '优点', '缺点', '好处', '坏处', '利弊', '得失', '成败', '对错', '是非', '真假', '虚实', '正负', '积极', '消极', '主动', '被动', '正面', '负面', '正向', '反向', '顺向', '逆向', '向前', '向后', '向上', '向下', '向左', '向右', '内部', '外部', '内在', '外在', '表面', '深层', '浅层', '底层', '顶层', '中层', '上层', '下层', '前端', '后端', '客户端', '服务端', '本地', '远程', '线上', '线下', '在线', '离线', '实时', '延时', '同步', '异步', '并行', '串行', '顺序', '随机', '固定', '变动', '静态', '动态', '稳定', '不稳定', '可靠', '不可靠', '安全', '不安全', '正常', '异常', '标准', '非标准', '合法', '非法', '有效', '无效', '可用', '不可用', '启用', '禁用', '开启', '关闭', '打开', '关闭', '连接', '断开', '登录', '登出', '进入', '退出', '加入', '离开', '参与', '退出', '开始', '结束', '启动', '停止', '运行', '暂停', '继续', '重启', '重置', '恢复', '还原', '备份', '同步', '更新', '刷新', '加载', '卸载', '安装', '卸载', '下载', '上传', '导入', '导出', '保存', '读取', '写入', '删除', '添加', '插入', '移除', '替换', '修改', '编辑', '复制', '粘贴', '剪切', '撤销', '重做', '查找', '搜索', '筛选', '过滤', '排序', '分组', '分类', '标记', '标签', '注释', '备注', '说明', '描述', '介绍', '概述', '详情', '摘要', '总结', '结论', '建议', '推荐', '提示', '警告', '注意', '重要', '关键', '核心', '主要', '次要', '辅助', '补充', '额外', '附加', '可选', '必选', '必须', '可以', '应该', '建议', '推荐', '不建议', '不推荐', '禁止', '允许', '支持', '不支持', '兼容', '不兼容', '适用', '不适用', '相关', '无关', '相似', '不同', '相同', '一致', '不一致', '匹配', '不匹配', '符合', '不符合', '满足', '不满足', '达到', '未达到', '超过', '不足', '足够', '不够', '充足', '缺乏', '丰富', '贫乏', '多样', '单一', '复杂', '简单', '困难', '容易', '方便', '不便', '快速', '缓慢', '及时', '延迟', '准确', '错误', '精确', '模糊', '清晰', '模糊', '明确', '不明确', '确定', '不确定', '肯定', '否定', '可能', '不可能', '必然', '偶然', '常见', '罕见', '普通', '特殊', '一般', '特别', '通用', '专用', '公共', '私有', '开放', '封闭', '公开', '保密', '透明', '不透明', '显示', '隐藏', '可见', '不可见', '明显', '隐蔽', '直接', '间接', '立即', '延后', '当前', '以前', '以后', '现在', '过去', '将来', '今天', '昨天', '明天', '本周', '上周', '下周', '本月', '上月', '下月', '今年', '去年', '明年', '最近', '最新', '最老', '最旧', '第一', '最后', '开头', '结尾', '前面', '后面', '上面', '下面', '左边', '右边', '中间', '边缘', '中心', '周围', '附近', '远处', '这里', '那里', '哪里', '到处', '处处', '各处', '某处', '何处', '此处', '彼处', '本地', '异地', '当地', '外地', '国内', '国外', '境内', '境外', '海内', '海外', '内地', '沿海', '城市', '农村', '市区', '郊区', '市中心', '市郊', 'downtown', '上城', '下城', '新城', '老城', '古城', '新区', '老区', '开发区', '工业区', '商业区', '住宅区', '文教区', '行政区', '经济区', '特区', '自贸区', '保税区', '高新区', '科技园', '工业园', '创业园', '孵化器', '加速器', '众创空间', '联合办公', '共享办公', '远程办公', '在家办公', '移动办公', '弹性办公', '灵活办公', '全职', '兼职', '实习', '试用', '正式', '临时', '短期', '长期', '永久', '固定', '流动', '稳定', '变动', '增长', '下降', '上升', '提高', '降低', '增加', '减少', '扩大', '缩小', '放大', '缩小', '增强', '减弱', '加强', '削弱', '提升', '下降', '改善', '恶化', '好转', '恶化', '进步', '退步', '发展', '倒退', '前进', '后退', '成长', '衰退', '繁荣', '萧条', '兴盛', '衰落', '崛起', '没落', '复兴', '衰败', '振兴', '衰微', '昌盛', '衰弱', '强盛', '衰落', '鼎盛', '衰败', '全盛', '衰微', '极盛', '衰落', '盛极', '衰败', '登峰', '造极', '如日', '中天', '蒸蒸', '日上', '欣欣', '向荣', '蓬勃', '发展', '方兴', '未艾', '朝气', '蓬勃', '生机', '勃勃', '充满', '活力', '富有', '生机', '生气', '勃勃', '精神', '饱满', '斗志', '昂扬', '意气', '风发', '神采', '飞扬', '容光', '焕发', '精神', '抖擞', '神清', '气爽', '心情', '舒畅', '情绪', '高涨', '兴致', '勃勃', '兴趣', '盎然', '津津', '有味', '乐此', '不疲', '乐在', '其中', '其乐', '无穷', '乐不', '思蜀', '流连', '忘返', '恋恋', '不舍', '依依', '不舍', '难舍', '难分', '藕断', '丝连', '千丝', '万缕', '千头', '万绪', '错综', '复杂', '盘根', '错节', '纵横', '交错', '交织', '一起', '相互', '交融', '融为', '一体', '浑然', '一体', '天衣', '无缝', '完美', '无缺', '十全', '十美', '尽善', '尽美', '精益', '求精', '精雕', '细琢', '精工', '细作', '精心', '制作', '用心', '良苦', '煞费', '苦心', '费尽', '心思', '绞尽', '脑汁', '苦思', '冥想', '深思', '熟虑', '三思', '而行', '慎重', '考虑', '仔细', '思考', '认真', '对待', '严肃', '认真', '一丝', '不苟', '精益', '求精', '力求', '完美', '追求', '卓越', '不断', '进步', '持续', '改进', '不断', '提高', '日益', '完善', '越来', '越好', '更上', '层楼', '百尺', '竿头', '更进', '一步', '精进', '不休', '永不', '停歇', '永不', '言败', '永不', '放弃', '坚持', '不懈', '持之', '以恒', '锲而', '不舍', '水滴', '石穿', '绳锯', '木断', '铁杵', '磨针', '功夫', '不负', '有心人', '天道', '酬勤', '一分', '耕耘', '一分', '收获', '种瓜', '得瓜', '种豆', '得豆', '善有', '善报', '恶有', '恶报', '因果', '报应', '自作', '自受', '咎由', '自取', '自食', '其果', '自作', '孽', '不可', '活', '玩火', '自焚', '搬起', '石头', '砸自己', '脚', '聪明', '反被', '聪明', '误', '机关', '算尽', '太聪明', '反误', '卿卿', '性命', '偷鸡', '不成', '蚀把', '米', '赔了', '夫人', '又折', '兵', '得不', '偿失', '因小', '失大', '捡了', '芝麻', '丢了', '西瓜', '小不', '忍则', '乱大', '谋', '一失', '足成', '千古', '恨', '一步', '错', '步步', '错', '差之', '毫厘', '谬以', '千里', '失之', '东隅', '收之', '桑榆', '塞翁', '失马', '焉知', '非福', '祸兮', '福所', '倚', '福兮', '祸所', '伏', '否极', '泰来', '苦尽', '甘来', '山重', '水复', '疑无', '路', '柳暗', '花明', '又一', '村', '车到', '山前', '必有', '路', '船到', '桥头', '自然', '直', '天无', '绝人', '之路', '山穷', '水尽', '疑无', '路', '柳暗', '花明', '又一', '村', '绝处', '逢生', '起死', '回生', '化险', '为夷', '转危', '为安', '逢凶', '化吉', '遇难', '成祥', '大难', '不死', '必有', '后福', '吉人', '天相', '好人', '好报', '善者', '善报', '德者', '德报', '仁者', '仁报', '义者', '义报', '礼者', '礼报', '智者', '智报', '信者', '信报', '忠者', '忠报', '孝者', '孝报', '廉者', '廉报', '耻者', '耻报', '勇者', '勇报', '毅者', '毅报', '恒者', '恒报', '诚者', '诚报', '真者', '真报', '善者', '善报', '美者', '美报', '爱者', '爱报', '和者', '和报', '平者', '平报', '安者', '安报', '乐者', '乐报', '喜者', '喜报', '福者', '福报', '寿者', '寿报', '康者', '康报', '健者', '健报', '强者', '强报', '壮者', '壮报', '盛者', '盛报', '兴者', '兴报', '旺者', '旺报', '发者', '发报', '达者', '达报', '成者', '成报', '功者', '功报', '胜者', '胜报', '赢者', '赢报', '得者', '得报', '获者', '获报', '取者', '取报', '收者', '收报', '益者', '益报', '利者', '利报', '盈者', '盈报', '赚者', '赚报', '挣者', '挣报', '赢者', '赢报', '胜者', '胜报', '成者', '成报', '功者', '功报', '达者', '达报', '发者', '发报', '旺者', '旺报', '兴者', '兴报', '盛者', '盛报', '强者', '强报', '壮者', '壮报', '健者', '健报', '康者', '康报', '寿者', '寿报', '福者', '福报', '喜者', '喜报', '乐者', '乐报', '安者', '安报', '平者', '平报', '和者', '和报', '爱者', '爱报', '美者', '美报', '善者', '善报', '真者', '真报', '诚者', '诚报', '恒者', '恒报', '毅者', '毅报', '勇者', '勇报', '耻者', '耻报', '廉者', '廉报', '孝者', '孝报', '忠者', '忠报', '信者', '信报', '智者', '智报', '礼者', '礼报', '义者', '义报', '仁者', '仁报', '德者', '德报']):
                abnormal_words.add(word)

        # 添加到自定义过滤词汇集合
        self.custom_filter_words.update(abnormal_words)

        print(f"识别出 {len(abnormal_words)} 个异常词汇")

        # 保存异常词汇到文件
        with open('filtered_words.txt', 'w', encoding='utf-8') as f:
            f.write("需要过滤的异常词汇列表：\n")
            f.write("=" * 50 + "\n")
            for i, word in enumerate(sorted(abnormal_words), 1):
                f.write(f"{i:3d}. {word}\n")

        print("异常词汇列表已保存到 filtered_words.txt")
        return abnormal_words

    def filter_words(self):
        """过滤异常词汇，生成最终的词频统计"""
        print("正在过滤异常词汇...")

        # 识别异常词汇
        abnormal_words = self.identify_abnormal_words()

        # 过滤词频统计
        filtered_freq = {}
        for word, freq in self.word_freq.items():
            if word not in self.custom_filter_words:
                filtered_freq[word] = freq

        self.word_freq = filtered_freq
        print(f"过滤完成，剩余 {len(self.word_freq)} 个有效词汇")

        return True

    def export_word_frequency(self, filename='word_frequency.xlsx'):
        """导出词频统计到Excel文件"""
        if not self.word_freq:
            print("错误：没有词频数据可供导出")
            return False

        print(f"正在导出词频统计到 {filename}...")

        # 按词频降序排列
        sorted_words = sorted(self.word_freq.items(), key=lambda x: x[1], reverse=True)

        # 创建DataFrame
        df = pd.DataFrame(sorted_words, columns=['词汇', '频次'])
        df['排名'] = range(1, len(df) + 1)
        df = df[['排名', '词汇', '频次']]  # 重新排列列顺序

        # 添加统计信息
        total_words = len(df)
        total_frequency = df['频次'].sum()

        # 创建统计摘要
        summary_data = {
            '统计项目': ['总词汇数', '总词频', '平均词频', '最高词频', '最低词频'],
            '数值': [
                total_words,
                total_frequency,
                round(total_frequency / total_words, 2),
                df['频次'].max(),
                df['频次'].min()
            ]
        }
        summary_df = pd.DataFrame(summary_data)

        # 保存到Excel文件
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 词频统计表
            df.to_excel(writer, sheet_name='词频统计', index=False)

            # 统计摘要表
            summary_df.to_excel(writer, sheet_name='统计摘要', index=False)

            # 前20高频词汇
            top20_df = df.head(20)
            top20_df.to_excel(writer, sheet_name='高频词汇TOP20', index=False)

        print(f"词频统计已成功导出到 {filename}")
        print(f"总计 {total_words} 个不同词汇，总词频 {total_frequency}")

        return True

    def create_mask_image(self):
        """创建词云遮罩图像"""
        print("正在创建词云遮罩图像...")

        # 创建一个简单的圆形遮罩
        width, height = 800, 600
        mask = np.zeros((height, width), dtype=np.uint8)

        # 创建圆形遮罩
        center_x, center_y = width // 2, height // 2
        radius = min(width, height) // 3

        y, x = np.ogrid[:height, :width]
        mask_circle = (x - center_x) ** 2 + (y - center_y) ** 2 <= radius ** 2
        mask[mask_circle] = 255

        # 保存遮罩图像
        mask_image = Image.fromarray(mask)
        mask_image.save('mask.png')

        print("遮罩图像已保存为 mask.png")
        return mask

    def generate_wordcloud(self, mask_file=None):
        """生成词云图"""
        if not self.word_freq:
            print("错误：没有词频数据可供生成词云")
            return False

        print("正在生成词云图...")

        # 如果没有提供遮罩文件，创建默认遮罩
        if mask_file is None or not os.path.exists(mask_file):
            mask = self.create_mask_image()
            mask_file = 'mask.png'
        else:
            # 加载遮罩图像
            mask_image = Image.open(mask_file)
            mask = np.array(mask_image)

        # 配置词云参数
        wordcloud = WordCloud(
            font_path=self.font_path,  # 中文字体
            width=800,
            height=600,
            background_color='white',
            mask=mask,
            max_words=200,  # 最多显示200个词
            relative_scaling=0.5,
            colormap='viridis',  # 颜色方案
            prefer_horizontal=0.7,  # 水平词汇比例
            min_font_size=10,
            max_font_size=100,
            random_state=42  # 固定随机种子，确保结果可重现
        ).generate_from_frequencies(self.word_freq)

        # 创建图形
        plt.figure(figsize=(12, 8))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('词云分析结果', fontsize=16, fontweight='bold', pad=20)

        # 保存词云图
        plt.tight_layout()
        plt.savefig('wordcloud_result.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("词云图已保存为 wordcloud_result.png")
        return True

    def run_analysis(self, url):
        """运行完整的词云分析流程"""
        print("=" * 60)
        print("开始词云分析")
        print("=" * 60)

        # 步骤1：获取网页文本
        if not self.fetch_text_from_url(url):
            print("分析失败：无法获取网页内容")
            return False

        # 步骤2：文本分词
        if not self.segment_text():
            print("分析失败：文本分词出错")
            return False

        # 步骤3：过滤异常词汇
        if not self.filter_words():
            print("分析失败：词汇过滤出错")
            return False

        # 步骤4：导出词频统计
        if not self.export_word_frequency():
            print("警告：词频统计导出失败")

        # 步骤5：生成词云图
        if not self.generate_wordcloud():
            print("分析失败：词云生成出错")
            return False

        print("=" * 60)
        print("词云分析完成！")
        print("=" * 60)
        print("生成的文件：")
        print("1. wordcloud_result.png - 词云图片")
        print("2. word_frequency.xlsx - 词频统计Excel文件")
        print("3. filtered_words.txt - 过滤词汇列表")
        print("4. mask.png - 词云遮罩图片")
        print("=" * 60)

        return True

    def print_top_words(self, n=20):
        """打印高频词汇"""
        if not self.word_freq:
            print("没有词频数据")
            return

        print(f"\n前{n}个高频词汇：")
        print("-" * 30)
        sorted_words = sorted(self.word_freq.items(), key=lambda x: x[1], reverse=True)
        for i, (word, freq) in enumerate(sorted_words[:n], 1):
            print(f"{i:2d}. {word:<10} ({freq}次)")


def main():
    """主函数"""
    # 目标URL
    url = "https://baijiahao.baidu.com/s?id=1832959729937061996&wfr=spider&for=pc"

    # 创建分析器实例
    analyzer = WordCloudAnalyzer()

    try:
        # 运行分析
        success = analyzer.run_analysis(url)

        if success:
            # 显示前20个高频词汇
            analyzer.print_top_words(20)

            print("\n分析完成！请查看生成的文件。")
        else:
            print("分析失败，请检查网络连接和URL是否正确。")

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
